package com.teammanage.service;

import com.teammanage.constants.InvitationStatusConstants;
import com.teammanage.constants.SubscriptionStatusConstants;
import com.teammanage.entity.AccountSubscription;
import com.teammanage.entity.TeamInvitation;
import com.teammanage.entity.TeamMember;
import com.teammanage.enums.TeamRole;
import com.teammanage.mapper.AccountSubscriptionMapper;
import com.teammanage.mapper.TeamInvitationMapper;
import com.teammanage.mapper.TeamMemberMapper;
import com.teammanage.util.StatusCodeConverter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 状态码迁移服务
 * 
 * 提供使用状态码的示例方法，展示如何在业务逻辑中使用状态标识符
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class StatusCodeMigrationService {
    
    private static final Logger log = LoggerFactory.getLogger(StatusCodeMigrationService.class);
    
    @Autowired
    private TeamMemberMapper teamMemberMapper;
    
    @Autowired
    private TeamInvitationMapper teamInvitationMapper;
    
    @Autowired
    private AccountSubscriptionMapper accountSubscriptionMapper;
    
    /**
     * 使用状态码创建团队邀请的示例方法
     */
    @Transactional
    public TeamInvitation createInvitationWithStatusCode(Long teamId, Long inviterId, String email, String message) {
        TeamInvitation invitation = new TeamInvitation();
        invitation.setTeamId(teamId);
        invitation.setInviterId(inviterId);
        invitation.setInviteeEmail(email);
        invitation.setMessage(message);
        invitation.setInvitedAt(LocalDateTime.now());
        invitation.setExpiresAt(LocalDateTime.now().plusDays(3));
        
        // 使用状态码设置状态
        invitation.setStatusCode(InvitationStatusConstants.PENDING_CODE);
        
        teamInvitationMapper.insert(invitation);
        
        log.info("使用状态码创建邀请成功: invitationId={}, statusCode={}", 
                invitation.getId(), invitation.getStatusCode());
        
        return invitation;
    }
    
    /**
     * 使用状态码响应邀请的示例方法
     */
    @Transactional
    public void respondToInvitationWithStatusCode(Long invitationId, Long userId, boolean accept) {
        TeamInvitation invitation = teamInvitationMapper.selectById(invitationId);
        if (invitation == null) {
            throw new RuntimeException("邀请不存在");
        }
        
        // 使用状态码检查是否可以响应
        if (!InvitationStatusConstants.canBeResponded(invitation.getStatusCode())) {
            throw new RuntimeException("邀请无法响应");
        }
        
        // 使用状态码设置新状态
        Integer newStatusCode = accept ? 
            InvitationStatusConstants.ACCEPTED_CODE : 
            InvitationStatusConstants.REJECTED_CODE;
        
        invitation.setStatusCode(newStatusCode);
        invitation.setInviteeId(userId);
        invitation.setRespondedAt(LocalDateTime.now());
        
        teamInvitationMapper.updateById(invitation);
        
        log.info("使用状态码响应邀请: invitationId={}, newStatusCode={}, accept={}", 
                invitationId, newStatusCode, accept);
    }
    
    /**
     * 使用状态码创建团队成员的示例方法
     */
    @Transactional
    public TeamMember createTeamMemberWithStatusCode(Long teamId, Long accountId, boolean isCreator) {
        TeamMember member = new TeamMember();
        member.setTeamId(teamId);
        member.setAccountId(accountId);
        member.setAssignedAt(LocalDateTime.now());
        member.setIsActive(true);
        member.setIsDeleted(false);
        
        // 使用状态码设置角色
        Integer roleCode = isCreator ? 
            TeamRole.StatusCodes.TEAM_CREATOR_CODE : 
            TeamRole.StatusCodes.TEAM_MEMBER_CODE;
        
        member.setRoleCode(roleCode);
        
        teamMemberMapper.insert(member);
        
        log.info("使用状态码创建团队成员: memberId={}, roleCode={}", 
                member.getId(), member.getRoleCode());
        
        return member;
    }
    
    /**
     * 使用状态码创建订阅的示例方法
     */
    @Transactional
    public AccountSubscription createSubscriptionWithStatusCode(Long accountId, Long planId) {
        AccountSubscription subscription = new AccountSubscription();
        subscription.setAccountId(accountId);
        subscription.setSubscriptionPlanId(planId);
        subscription.setStartDate(java.time.LocalDate.now());
        subscription.setEndDate(java.time.LocalDate.now().plusMonths(1));
        
        // 使用状态码设置状态
        subscription.setStatusCode(SubscriptionStatusConstants.ACTIVE_CODE);
        
        accountSubscriptionMapper.insert(subscription);
        
        log.info("使用状态码创建订阅: subscriptionId={}, statusCode={}", 
                subscription.getId(), subscription.getStatusCode());
        
        return subscription;
    }
    
    /**
     * 使用状态码查询待处理邀请的示例方法
     */
    public List<TeamInvitation> findPendingInvitationsWithStatusCode(String email) {
        // 这里需要在Mapper中添加相应的查询方法
        // 示例：SELECT * FROM team_invitation WHERE invitee_email = ? AND status_code = ?
        
        log.info("使用状态码查询待处理邀请: email={}, statusCode={}", 
                email, InvitationStatusConstants.PENDING_CODE);
        
        // 返回空列表作为示例
        return List.of();
    }
    
    /**
     * 使用状态码检查权限的示例方法
     */
    public boolean hasManagePermissionWithStatusCode(Integer roleCode) {
        // 使用状态码进行权限检查
        return TeamRole.StatusCodes.TEAM_CREATOR_CODE.equals(roleCode);
    }
    
    /**
     * 状态码转换示例方法
     */
    public void demonstrateStatusCodeConversion() {
        // 枚举转状态码
        Integer creatorCode = StatusCodeConverter.TeamRoleConverter.toCode(TeamRole.TEAM_CREATOR);
        Integer pendingCode = StatusCodeConverter.InvitationStatusConverter.toCode(TeamInvitation.InvitationStatus.PENDING);
        Integer activeCode = StatusCodeConverter.SubscriptionStatusConverter.toCode(AccountSubscription.SubscriptionStatus.ACTIVE);
        
        log.info("枚举转状态码示例: creatorCode={}, pendingCode={}, activeCode={}", 
                creatorCode, pendingCode, activeCode);
        
        // 状态码转枚举
        TeamRole role = StatusCodeConverter.TeamRoleConverter.fromCode(100);
        TeamInvitation.InvitationStatus status = StatusCodeConverter.InvitationStatusConverter.fromCode(1);
        AccountSubscription.SubscriptionStatus subStatus = StatusCodeConverter.SubscriptionStatusConverter.fromCode(1);
        
        log.info("状态码转枚举示例: role={}, status={}, subStatus={}", 
                role, status, subStatus);
    }
}
