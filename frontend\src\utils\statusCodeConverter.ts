import {
  TeamRole,
  TeamRoleStatusCodes,
  TeamRoleStatusCode,
  InvitationStatus,
  InvitationStatusCodes,
  InvitationStatusCode,
  SubscriptionStatusCodes,
  SubscriptionStatusCode,
} from '../types/api';

/**
 * 状态码转换工具类
 * 
 * 提供枚举与状态码之间的转换方法，支持渐进式迁移
 */

/**
 * 团队角色转换工具
 */
export class TeamRoleConverter {
  /**
   * 枚举转状态码
   */
  static toCode(role: TeamRole): TeamRoleStatusCode | null {
    switch (role) {
      case TeamRole.TEAM_CREATOR:
        return TeamRoleStatusCodes.TEAM_CREATOR;
      case TeamRole.TEAM_MEMBER:
        return TeamRoleStatusCodes.TEAM_MEMBER;
      default:
        return null;
    }
  }

  /**
   * 状态码转枚举
   */
  static fromCode(code: TeamRoleStatusCode): TeamRole | null {
    switch (code) {
      case TeamRoleStatusCodes.TEAM_CREATOR:
        return TeamRole.TEAM_CREATOR;
      case TeamRoleStatusCodes.TEAM_MEMBER:
        return TeamRole.TEAM_MEMBER;
      default:
        return null;
    }
  }

  /**
   * 检查状态码是否有效
   */
  static isValidCode(code: number): code is TeamRoleStatusCode {
    return Object.values(TeamRoleStatusCodes).includes(code as TeamRoleStatusCode);
  }

  /**
   * 获取角色显示名称
   */
  static getDisplayName(code: TeamRoleStatusCode): string {
    switch (code) {
      case TeamRoleStatusCodes.TEAM_CREATOR:
        return '团队创建者';
      case TeamRoleStatusCodes.TEAM_MEMBER:
        return '团队成员';
      default:
        return '未知角色';
    }
  }

  /**
   * 检查是否有管理权限
   */
  static hasManagePermission(code: TeamRoleStatusCode): boolean {
    return code === TeamRoleStatusCodes.TEAM_CREATOR;
  }
}

/**
 * 邀请状态转换工具
 */
export class InvitationStatusConverter {
  /**
   * 枚举转状态码
   */
  static toCode(status: InvitationStatus): InvitationStatusCode | null {
    switch (status) {
      case InvitationStatus.PENDING:
        return InvitationStatusCodes.PENDING;
      case InvitationStatus.ACCEPTED:
        return InvitationStatusCodes.ACCEPTED;
      case InvitationStatus.REJECTED:
        return InvitationStatusCodes.REJECTED;
      case InvitationStatus.EXPIRED:
        return InvitationStatusCodes.EXPIRED;
      case InvitationStatus.CANCELLED:
        return InvitationStatusCodes.CANCELLED;
      default:
        return null;
    }
  }

  /**
   * 状态码转枚举
   */
  static fromCode(code: InvitationStatusCode): InvitationStatus | null {
    switch (code) {
      case InvitationStatusCodes.PENDING:
        return InvitationStatus.PENDING;
      case InvitationStatusCodes.ACCEPTED:
        return InvitationStatus.ACCEPTED;
      case InvitationStatusCodes.REJECTED:
        return InvitationStatus.REJECTED;
      case InvitationStatusCodes.EXPIRED:
        return InvitationStatus.EXPIRED;
      case InvitationStatusCodes.CANCELLED:
        return InvitationStatus.CANCELLED;
      default:
        return null;
    }
  }

  /**
   * 检查状态码是否有效
   */
  static isValidCode(code: number): code is InvitationStatusCode {
    return Object.values(InvitationStatusCodes).includes(code as InvitationStatusCode);
  }

  /**
   * 获取状态显示名称
   */
  static getDisplayName(code: InvitationStatusCode): string {
    switch (code) {
      case InvitationStatusCodes.PENDING:
        return '待确认';
      case InvitationStatusCodes.ACCEPTED:
        return '已确认';
      case InvitationStatusCodes.REJECTED:
        return '已拒绝';
      case InvitationStatusCodes.EXPIRED:
        return '已过期';
      case InvitationStatusCodes.CANCELLED:
        return '已取消';
      default:
        return '未知状态';
    }
  }

  /**
   * 检查状态是否可以被响应
   */
  static canBeResponded(code: InvitationStatusCode): boolean {
    return code === InvitationStatusCodes.PENDING;
  }

  /**
   * 检查状态是否可以被取消
   */
  static canBeCancelled(code: InvitationStatusCode): boolean {
    return code === InvitationStatusCodes.PENDING;
  }

  /**
   * 检查状态是否已过期
   */
  static isExpired(code: InvitationStatusCode): boolean {
    return code === InvitationStatusCodes.EXPIRED;
  }
}

/**
 * 订阅状态转换工具
 */
export class SubscriptionStatusConverter {
  /**
   * 检查状态码是否有效
   */
  static isValidCode(code: number): code is SubscriptionStatusCode {
    return Object.values(SubscriptionStatusCodes).includes(code as SubscriptionStatusCode);
  }

  /**
   * 获取状态显示名称
   */
  static getDisplayName(code: SubscriptionStatusCode): string {
    switch (code) {
      case SubscriptionStatusCodes.ACTIVE:
        return '激活';
      case SubscriptionStatusCodes.EXPIRED:
        return '过期';
      case SubscriptionStatusCodes.CANCELED:
        return '取消';
      default:
        return '未知状态';
    }
  }

  /**
   * 检查状态是否为激活状态
   */
  static isActive(code: SubscriptionStatusCode): boolean {
    return code === SubscriptionStatusCodes.ACTIVE;
  }

  /**
   * 检查状态是否已过期
   */
  static isExpired(code: SubscriptionStatusCode): boolean {
    return code === SubscriptionStatusCodes.EXPIRED;
  }

  /**
   * 检查状态是否已取消
   */
  static isCanceled(code: SubscriptionStatusCode): boolean {
    return code === SubscriptionStatusCodes.CANCELED;
  }
}

/**
 * 状态码转换器的统一导出
 */
export const StatusCodeConverter = {
  TeamRole: TeamRoleConverter,
  InvitationStatus: InvitationStatusConverter,
  SubscriptionStatus: SubscriptionStatusConverter,
};
