package com.teammanage.entity;

import java.time.LocalDateTime;
import java.util.Objects;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.teammanage.enums.TeamRole;

/**
 * 团队成员实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */

@TableName("team_member")
public class TeamMember extends BaseEntity {

    /**
     * 团队ID
     */
    private Long teamId;

    /**
     * 用户ID
     */
    private Long accountId;

    /**
     * 是否为创建者
     * 与 role 字段共存，用于不同的业务场景
     */
    private Boolean isCreator;

    /**
     * 团队角色
     * 与 isCreator 字段共存，用于不同的业务场景
     */
    private TeamRole role;

    /**
     * 团队角色状态码
     * 新的状态标识符字段，与 role 字段保持同步
     */
    private Integer roleCode;

    /**
     * 分配时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime assignedAt;

    /**
     * 最后访问时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastAccessTime;

    /**
     * 账号状态
     */
    private Boolean isActive;

    /**
     * 删除标记
     */
    @TableLogic
    private Boolean isDeleted;

    // 手动添加getter/setter方法
    public Long getTeamId() { return teamId; }
    public void setTeamId(Long teamId) { this.teamId = teamId; }

    public Long getAccountId() { return accountId; }
    public void setAccountId(Long accountId) { this.accountId = accountId; }

    /**
     * 获取是否为创建者
     * 与 getRole() 方法共存，用于不同的业务场景
     */
    public Boolean getIsCreator() { return isCreator; }

    /**
     * 设置是否为创建者
     * 与 setRole() 方法共存，用于不同的业务场景
     */
    public void setIsCreator(Boolean isCreator) {
        this.isCreator = isCreator;
        // 同步更新role字段以保持一致性
        this.role = TeamRole.fromIsCreator(Boolean.TRUE.equals(isCreator));
        // 同步更新roleCode字段
        this.roleCode = this.role != null ? TeamRole.StatusCodes.toCode(this.role) : null;
    }

    public TeamRole getRole() { return role; }

    public void setRole(TeamRole role) {
        this.role = role;
        // 同步更新isCreator字段以保持向后兼容性
        this.isCreator = role != null && role.toIsCreator();
        // 同步更新roleCode字段
        this.roleCode = role != null ? TeamRole.StatusCodes.toCode(role) : null;
    }

    /**
     * 获取团队角色状态码
     *
     * @return 角色状态码
     */
    public Integer getRoleCode() { return roleCode; }

    /**
     * 设置团队角色状态码
     * 同时同步更新role和isCreator字段
     *
     * @param roleCode 角色状态码
     */
    public void setRoleCode(Integer roleCode) {
        this.roleCode = roleCode;
        // 同步更新role字段
        this.role = roleCode != null ? TeamRole.StatusCodes.fromCode(roleCode) : null;
        // 同步更新isCreator字段
        this.isCreator = this.role != null ? this.role.toIsCreator() : null;
    }

    public LocalDateTime getAssignedAt() { return assignedAt; }
    public void setAssignedAt(LocalDateTime assignedAt) { this.assignedAt = assignedAt; }

    public LocalDateTime getLastAccessTime() { return lastAccessTime; }
    public void setLastAccessTime(LocalDateTime lastAccessTime) { this.lastAccessTime = lastAccessTime; }

    public Boolean getIsActive() { return isActive; }
    public void setIsActive(Boolean isActive) { this.isActive = isActive; }

    public Boolean getIsDeleted() { return isDeleted; }
    public void setIsDeleted(Boolean isDeleted) { this.isDeleted = isDeleted; }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        TeamMember that = (TeamMember) o;
        return Objects.equals(teamId, that.teamId) &&
               Objects.equals(accountId, that.accountId) &&
               Objects.equals(role, that.role) &&
               Objects.equals(assignedAt, that.assignedAt) &&
               Objects.equals(lastAccessTime, that.lastAccessTime) &&
               Objects.equals(isActive, that.isActive) &&
               Objects.equals(isDeleted, that.isDeleted);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), teamId, accountId, role, assignedAt, lastAccessTime, isActive, isDeleted);
    }

    // 角色相关的便利方法

    /**
     * 检查是否可以管理团队
     *
     * @return 是否可以管理团队
     */
    public boolean canManageTeam() {
        return role != null && role.canManageTeam();
    }

    /**
     * 检查是否可以管理成员
     *
     * @return 是否可以管理成员
     */
    public boolean canManageMembers() {
        return role != null && role.canManageMembers();
    }

    /**
     * 检查是否可以访问数据
     *
     * @return 是否可以访问数据
     */
    public boolean canAccessData() {
        return role != null && role.canAccessData();
    }

    /**
     * 检查角色权限是否高于或等于指定角色
     *
     * @param other 要比较的角色
     * @return 是否有更高或相等的权限
     */
    public boolean hasPermissionLevel(TeamRole other) {
        return role != null && role.hasPermissionLevel(other);
    }

    // 团队访问控制相关的便利方法

    /**
     * 检查成员是否处于活跃状态
     *
     * @return 是否活跃
     */
    public boolean isActiveStatus() {
        return Boolean.TRUE.equals(isActive);
    }

    /**
     * 检查成员是否被禁用
     *
     * @return 是否被禁用
     */
    public boolean isDisabled() {
        return !Boolean.TRUE.equals(isActive);
    }

    /**
     * 检查成员是否被停用（与禁用同义）
     *
     * @return 是否被停用
     */
    public boolean isDeactivated() {
        return isDisabled();
    }

    /**
     * 检查成员是否可以访问团队
     * 综合考虑删除状态和活跃状态
     *
     * @return 是否可以访问团队
     */
    public boolean canAccessTeam() {
        // 已删除的成员不能访问
        if (Boolean.TRUE.equals(isDeleted)) {
            return false;
        }

        // 非活跃状态的成员不能访问
        if (!isActiveStatus()) {
            return false;
        }

        return true;
    }

    /**
     * 检查成员是否应该在团队列表中显示
     * 即使被禁用也应该显示，但已删除的不显示
     *
     * @return 是否应该在列表中显示
     */
    public boolean shouldShowInTeamList() {
        // 已删除的成员不显示
        return !Boolean.TRUE.equals(isDeleted);
    }

    /**
     * 获取成员状态的描述信息
     *
     * @return 状态描述
     */
    public String getStatusDescription() {
        if (Boolean.TRUE.equals(isDeleted)) {
            return "已删除";
        }

        if (!isActiveStatus()) {
            return "已停用";
        }

        return "活跃";
    }

    /**
     * 获取访问被拒绝时的错误消息
     *
     * @return 错误消息
     */
    public String getAccessDeniedMessage() {
        if (Boolean.TRUE.equals(isDeleted)) {
            return "您已不是该团队的成员";
        }

        if (!isActiveStatus()) {
            return "您的账户已在此团队中被停用";
        }

        return "无法访问该团队";
    }

}
