package com.teammanage.entity;

import java.time.LocalDate;
import java.util.Objects;

import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 用户订阅记录实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */

@TableName("account_subscription")
public class AccountSubscription extends BaseEntity {

    /**
     * 用户ID
     */
    private Long accountId;

    /**
     * 套餐ID
     */
    private Long subscriptionPlanId;

    /**
     * 开始日期
     */
    private LocalDate startDate;

    /**
     * 结束日期
     */
    private LocalDate endDate;

    /**
     * 订阅状态
     */
    private SubscriptionStatus status;

    /**
     * 订阅状态码
     * 新的状态标识符字段，与 status 字段保持同步
     */
    private Integer statusCode;

    /**
     * 订阅状态枚举
     */
    public enum SubscriptionStatus {
        ACTIVE,   // 激活
        EXPIRED,  // 过期
        CANCELED  // 取消
    }

    // Getter and Setter methods
    public Long getAccountId() { return accountId; }
    public void setAccountId(Long accountId) { this.accountId = accountId; }

    public Long getSubscriptionPlanId() { return subscriptionPlanId; }
    public void setSubscriptionPlanId(Long subscriptionPlanId) { this.subscriptionPlanId = subscriptionPlanId; }

    public LocalDate getStartDate() { return startDate; }
    public void setStartDate(LocalDate startDate) { this.startDate = startDate; }

    public LocalDate getEndDate() { return endDate; }
    public void setEndDate(LocalDate endDate) { this.endDate = endDate; }

    public SubscriptionStatus getStatus() { return status; }
    public void setStatus(SubscriptionStatus status) {
        this.status = status;
        // 同步更新statusCode字段
        this.statusCode = status != null ? com.teammanage.constants.SubscriptionStatusConstants.toCode(status) : null;
    }

    /**
     * 获取订阅状态码
     *
     * @return 状态码
     */
    public Integer getStatusCode() { return statusCode; }

    /**
     * 设置订阅状态码
     * 同时同步更新status字段
     *
     * @param statusCode 状态码
     */
    public void setStatusCode(Integer statusCode) {
        this.statusCode = statusCode;
        // 同步更新status字段
        this.status = statusCode != null ? com.teammanage.constants.SubscriptionStatusConstants.fromCode(statusCode) : null;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        AccountSubscription that = (AccountSubscription) o;
        return Objects.equals(accountId, that.accountId) &&
               Objects.equals(subscriptionPlanId, that.subscriptionPlanId) &&
               Objects.equals(startDate, that.startDate) &&
               Objects.equals(endDate, that.endDate) &&
               status == that.status;
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), accountId, subscriptionPlanId, startDate, endDate, status);
    }

}
