package com.teammanage.entity;

import java.time.LocalDateTime;
import java.util.Objects;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 团队邀请实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@TableName("team_invitation")
public class TeamInvitation extends BaseEntity {

    /**
     * 团队ID
     */
    private Long teamId;

    /**
     * 邀请人ID
     */
    private Long inviterId;

    /**
     * 被邀请人邮箱
     */
    private String inviteeEmail;

    /**
     * 被邀请人ID（邀请时可能为空）
     */
    private Long inviteeId;

    /**
     * 邀请状态
     */
    private InvitationStatus status;

    /**
     * 邀请状态码
     * 新的状态标识符字段，与 status 字段保持同步
     */
    private Integer statusCode;

    /**
     * 邀请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime invitedAt;

    /**
     * 响应时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime respondedAt;

    /**
     * 过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expiresAt;

    /**
     * 邀请消息
     */
    private String message;

    /**
     * 邀请状态枚举
     */
    public enum InvitationStatus {
        PENDING,    // 待确认
        ACCEPTED,   // 已确认
        REJECTED,   // 已拒绝
        EXPIRED,    // 已过期
        CANCELLED   // 已取消
    }

    // Getter and Setter methods
    public Long getTeamId() { return teamId; }
    public void setTeamId(Long teamId) { this.teamId = teamId; }

    public Long getInviterId() { return inviterId; }
    public void setInviterId(Long inviterId) { this.inviterId = inviterId; }

    public String getInviteeEmail() { return inviteeEmail; }
    public void setInviteeEmail(String inviteeEmail) { this.inviteeEmail = inviteeEmail; }

    public Long getInviteeId() { return inviteeId; }
    public void setInviteeId(Long inviteeId) { this.inviteeId = inviteeId; }

    public InvitationStatus getStatus() { return status; }
    public void setStatus(InvitationStatus status) {
        this.status = status;
        // 同步更新statusCode字段
        this.statusCode = status != null ? com.teammanage.constants.InvitationStatusConstants.toCode(status) : null;
    }

    /**
     * 获取邀请状态码
     *
     * @return 状态码
     */
    public Integer getStatusCode() { return statusCode; }

    /**
     * 设置邀请状态码
     * 同时同步更新status字段
     *
     * @param statusCode 状态码
     */
    public void setStatusCode(Integer statusCode) {
        this.statusCode = statusCode;
        // 同步更新status字段
        this.status = statusCode != null ? com.teammanage.constants.InvitationStatusConstants.fromCode(statusCode) : null;
    }

    public LocalDateTime getInvitedAt() { return invitedAt; }
    public void setInvitedAt(LocalDateTime invitedAt) { this.invitedAt = invitedAt; }

    public LocalDateTime getRespondedAt() { return respondedAt; }
    public void setRespondedAt(LocalDateTime respondedAt) { this.respondedAt = respondedAt; }

    public LocalDateTime getExpiresAt() { return expiresAt; }
    public void setExpiresAt(LocalDateTime expiresAt) { this.expiresAt = expiresAt; }

    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }

    /**
     * 检查邀请是否已过期
     */
    public boolean isExpired() {
        return LocalDateTime.now().isAfter(expiresAt);
    }

    /**
     * 检查邀请是否可以被响应
     */
    public boolean canBeResponded() {
        return status == InvitationStatus.PENDING && !isExpired();
    }

    /**
     * 检查邀请是否可以被取消
     */
    public boolean canBeCancelled() {
        return status == InvitationStatus.PENDING;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        TeamInvitation that = (TeamInvitation) o;
        return Objects.equals(teamId, that.teamId) &&
               Objects.equals(inviterId, that.inviterId) &&
               Objects.equals(inviteeEmail, that.inviteeEmail) &&
               Objects.equals(inviteeId, that.inviteeId) &&
               status == that.status &&
               Objects.equals(invitedAt, that.invitedAt) &&
               Objects.equals(respondedAt, that.respondedAt) &&
               Objects.equals(expiresAt, that.expiresAt) &&
               Objects.equals(message, that.message);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), teamId, inviterId, inviteeEmail, 
                          inviteeId, status, invitedAt, respondedAt, expiresAt, message);
    }

    @Override
    public String toString() {
        return "TeamInvitation{" +
                "id=" + getId() +
                ", teamId=" + teamId +
                ", inviterId=" + inviterId +
                ", inviteeEmail='" + inviteeEmail + '\'' +
                ", inviteeId=" + inviteeId +
                ", status=" + status +
                ", invitedAt=" + invitedAt +
                ", respondedAt=" + respondedAt +
                ", expiresAt=" + expiresAt +
                ", message='" + message + '\'' +
                '}';
    }
}
