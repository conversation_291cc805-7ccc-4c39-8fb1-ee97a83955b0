package com.teammanage.entity;

import com.teammanage.constants.InvitationStatusConstants;
import com.teammanage.constants.SubscriptionStatusConstants;
import com.teammanage.entity.AccountSubscription.SubscriptionStatus;
import com.teammanage.entity.TeamInvitation.InvitationStatus;
import com.teammanage.enums.TeamRole;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 状态码同步测试
 * 
 * 测试实体类中枚举字段和状态码字段的同步机制
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class StatusCodeSyncTest {

    @Test
    public void testTeamMemberRoleSync() {
        TeamMember member = new TeamMember();

        // 测试通过枚举设置角色
        member.setRole(TeamRole.TEAM_CREATOR);
        assertEquals(TeamRole.TEAM_CREATOR, member.getRole());
        assertEquals(Integer.valueOf(100), member.getRoleCode());
        assertTrue(member.getIsCreator());

        member.setRole(TeamRole.TEAM_MEMBER);
        assertEquals(TeamRole.TEAM_MEMBER, member.getRole());
        assertEquals(Integer.valueOf(10), member.getRoleCode());
        assertFalse(member.getIsCreator());

        // 测试通过状态码设置角色
        member.setRoleCode(100);
        assertEquals(TeamRole.TEAM_CREATOR, member.getRole());
        assertEquals(Integer.valueOf(100), member.getRoleCode());
        assertTrue(member.getIsCreator());

        member.setRoleCode(10);
        assertEquals(TeamRole.TEAM_MEMBER, member.getRole());
        assertEquals(Integer.valueOf(10), member.getRoleCode());
        assertFalse(member.getIsCreator());

        // 测试通过isCreator设置角色
        member.setIsCreator(true);
        assertEquals(TeamRole.TEAM_CREATOR, member.getRole());
        assertEquals(Integer.valueOf(100), member.getRoleCode());
        assertTrue(member.getIsCreator());

        member.setIsCreator(false);
        assertEquals(TeamRole.TEAM_MEMBER, member.getRole());
        assertEquals(Integer.valueOf(10), member.getRoleCode());
        assertFalse(member.getIsCreator());

        // 测试null值处理
        member.setRole(null);
        assertNull(member.getRole());
        assertNull(member.getRoleCode());
        assertNull(member.getIsCreator());

        member.setRoleCode(null);
        assertNull(member.getRole());
        assertNull(member.getRoleCode());
        assertNull(member.getIsCreator());
    }

    @Test
    public void testTeamInvitationStatusSync() {
        TeamInvitation invitation = new TeamInvitation();

        // 测试通过枚举设置状态
        invitation.setStatus(InvitationStatus.PENDING);
        assertEquals(InvitationStatus.PENDING, invitation.getStatus());
        assertEquals(Integer.valueOf(1), invitation.getStatusCode());

        invitation.setStatus(InvitationStatus.ACCEPTED);
        assertEquals(InvitationStatus.ACCEPTED, invitation.getStatus());
        assertEquals(Integer.valueOf(2), invitation.getStatusCode());

        invitation.setStatus(InvitationStatus.REJECTED);
        assertEquals(InvitationStatus.REJECTED, invitation.getStatus());
        assertEquals(Integer.valueOf(3), invitation.getStatusCode());

        invitation.setStatus(InvitationStatus.EXPIRED);
        assertEquals(InvitationStatus.EXPIRED, invitation.getStatus());
        assertEquals(Integer.valueOf(4), invitation.getStatusCode());

        invitation.setStatus(InvitationStatus.CANCELLED);
        assertEquals(InvitationStatus.CANCELLED, invitation.getStatus());
        assertEquals(Integer.valueOf(5), invitation.getStatusCode());

        // 测试通过状态码设置状态
        invitation.setStatusCode(1);
        assertEquals(InvitationStatus.PENDING, invitation.getStatus());
        assertEquals(Integer.valueOf(1), invitation.getStatusCode());

        invitation.setStatusCode(2);
        assertEquals(InvitationStatus.ACCEPTED, invitation.getStatus());
        assertEquals(Integer.valueOf(2), invitation.getStatusCode());

        invitation.setStatusCode(3);
        assertEquals(InvitationStatus.REJECTED, invitation.getStatus());
        assertEquals(Integer.valueOf(3), invitation.getStatusCode());

        invitation.setStatusCode(4);
        assertEquals(InvitationStatus.EXPIRED, invitation.getStatus());
        assertEquals(Integer.valueOf(4), invitation.getStatusCode());

        invitation.setStatusCode(5);
        assertEquals(InvitationStatus.CANCELLED, invitation.getStatus());
        assertEquals(Integer.valueOf(5), invitation.getStatusCode());

        // 测试null值处理
        invitation.setStatus(null);
        assertNull(invitation.getStatus());
        assertNull(invitation.getStatusCode());

        invitation.setStatusCode(null);
        assertNull(invitation.getStatus());
        assertNull(invitation.getStatusCode());
    }

    @Test
    public void testAccountSubscriptionStatusSync() {
        AccountSubscription subscription = new AccountSubscription();

        // 测试通过枚举设置状态
        subscription.setStatus(SubscriptionStatus.ACTIVE);
        assertEquals(SubscriptionStatus.ACTIVE, subscription.getStatus());
        assertEquals(Integer.valueOf(1), subscription.getStatusCode());

        subscription.setStatus(SubscriptionStatus.EXPIRED);
        assertEquals(SubscriptionStatus.EXPIRED, subscription.getStatus());
        assertEquals(Integer.valueOf(2), subscription.getStatusCode());

        subscription.setStatus(SubscriptionStatus.CANCELED);
        assertEquals(SubscriptionStatus.CANCELED, subscription.getStatus());
        assertEquals(Integer.valueOf(3), subscription.getStatusCode());

        // 测试通过状态码设置状态
        subscription.setStatusCode(1);
        assertEquals(SubscriptionStatus.ACTIVE, subscription.getStatus());
        assertEquals(Integer.valueOf(1), subscription.getStatusCode());

        subscription.setStatusCode(2);
        assertEquals(SubscriptionStatus.EXPIRED, subscription.getStatus());
        assertEquals(Integer.valueOf(2), subscription.getStatusCode());

        subscription.setStatusCode(3);
        assertEquals(SubscriptionStatus.CANCELED, subscription.getStatus());
        assertEquals(Integer.valueOf(3), subscription.getStatusCode());

        // 测试null值处理
        subscription.setStatus(null);
        assertNull(subscription.getStatus());
        assertNull(subscription.getStatusCode());

        subscription.setStatusCode(null);
        assertNull(subscription.getStatus());
        assertNull(subscription.getStatusCode());
    }

    @Test
    public void testBusinessLogicWithStatusCodes() {
        // 测试邀请业务逻辑
        TeamInvitation invitation = new TeamInvitation();
        
        invitation.setStatusCode(InvitationStatusConstants.PENDING_CODE);
        assertTrue(invitation.canBeResponded());
        assertTrue(invitation.canBeCancelled());

        invitation.setStatusCode(InvitationStatusConstants.ACCEPTED_CODE);
        assertFalse(invitation.canBeResponded());
        assertFalse(invitation.canBeCancelled());

        invitation.setStatusCode(InvitationStatusConstants.EXPIRED_CODE);
        assertFalse(invitation.canBeResponded());
        assertFalse(invitation.canBeCancelled());

        // 测试团队成员权限逻辑
        TeamMember member = new TeamMember();
        
        member.setRoleCode(TeamRole.StatusCodes.TEAM_CREATOR_CODE);
        assertTrue(member.getRole().canManageTeam());
        assertTrue(member.getRole().canManageMembers());

        member.setRoleCode(TeamRole.StatusCodes.TEAM_MEMBER_CODE);
        assertFalse(member.getRole().canManageTeam());
        assertFalse(member.getRole().canManageMembers());
    }
}
